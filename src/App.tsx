import Calendar from './components/Calendar'
import AuthPage from './components/AuthPage'
import { useState, useEffect } from 'react'
import { useLocalStorage } from './hooks/useLocalStorage'
import { AuthProvider, useAuth } from './contexts/AuthContext'

const MainApp: React.FC = () => {
  const [darkMode, setDarkMode] = useState(false);
  const [planName, setPlanName] = useLocalStorage('planName', 'Navn');
  const { user, isLoading } = useAuth();

  // Detect system preference for dark mode
  useEffect(() => {
    // Check if the user's system prefers dark mode
    const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    setDarkMode(prefersDarkMode);

    // Listen for changes in the system preference
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setDarkMode(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Apply dark mode class to document
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  if (isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center transition-colors duration-200 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className={`mt-4 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <AuthPage darkMode={darkMode} />;
  }

  return (
    <div className={`min-h-screen py-8 transition-colors duration-200 flex flex-col items-center ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* App title */}
      <div className="w-full text-center mb-8 max-w-6xl mx-auto">
        <div className="relative inline-block">
          <input
            type="text"
            value={planName}
            onChange={(e) => setPlanName(e.target.value)}
            className={`text-3xl font-bold text-center transition-colors duration-200 border-b-2 focus:outline-none focus:border-indigo-500 bg-transparent ${darkMode ? 'text-white border-gray-700' : 'text-gray-800 border-gray-300'}`}
            placeholder="Navn"
          />
          <div className={`absolute -right-6 top-1/2 transform -translate-y-1/2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Calendar component */}
      <div className="w-full flex justify-center px-4 flex-grow">
        <Calendar darkMode={darkMode} planName={planName} />
      </div>

      {/* Footer */}
      <footer className={`w-full py-4 mt-8 text-center transition-colors duration-200 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        <a
          href="https://www.faseplan.dk"
          target="_blank"
          rel="noopener noreferrer"
          className="hover:underline transition-colors duration-200 font-medium"
        >
          www.faseplan.dk
        </a>
      </footer>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <MainApp />
    </AuthProvider>
  );
}

export default App
